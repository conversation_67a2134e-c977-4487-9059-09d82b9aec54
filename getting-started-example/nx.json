{"$schema": "node_modules/nx/schemas/nx-schema.json", "tasksRunnerOptions": {"default": {"runner": "nx/tasks-runners/default", "options": {"cacheableOperations": ["build", "test"]}}}, "namedInputs": {"default": ["{projectRoot}/**/*"], "prod": ["!{projectRoot}/**/*.spec.tsx"], "production": ["default"]}, "targetDefaults": {"build": {"inputs": ["prod", "^prod"], "dependsOn": ["^build"]}, "test": {"inputs": ["default", "^prod"]}, "dev": {"dependsOn": ["^build"]}}}