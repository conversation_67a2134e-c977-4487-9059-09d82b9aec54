{"name": "footer", "version": "0.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "rimraf dist && rollup --config", "test": "jest"}, "dependencies": {"@testing-library/react": "13.3.0", "@types/jest": "^27.0.3", "@types/react": "^17.0.14", "jest": "^27.4.3", "react": "18.1.0", "react-dom": "18.1.0", "rollup": "^2.60.2", "rollup-plugin-typescript2": "^0.31.1", "ts-jest": "^27.1.1", "tslib": "^2.3.1", "typescript": "^4.5.2"}, "devDependencies": {"rimraf": "^3.0.2"}}